package com.sayweee.weee.react;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.PersistableBundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.facebook.react.ReactActivity;
import com.facebook.react.ReactActivityDelegate;
import com.facebook.react.ReactBaseActivity;
import com.facebook.react.ReactInstanceEventListener;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.WritableMap;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.react.module.WeeeReactRouteModule;

//
// Created by <PERSON><PERSON> on 18/06/2025.
//
public class ReactMainActivity extends ReactBaseActivity {

    private static final String BUNDLE_COMPONENT_NAME = "bundle_component_name";

    public static Intent getIntent(Context context, String componentName) {
        return new Intent(context, ReactMainActivity.class)
                .putExtra(BUNDLE_COMPONENT_NAME, componentName);
    }

    @Nullable
    @Override
    protected String getMainComponentName() {
        if (getIntent() == null) {
            finish();
            return "";
        }
        return getIntent().getStringExtra(BUNDLE_COMPONENT_NAME);
    }

    @Override
    protected ReactActivityDelegate createReactActivityDelegate() {
        return new ReactActivityDelegate(this, getMainComponentName()) {
            @Nullable
            @Override
            protected Bundle getLaunchOptions() {
                Bundle initProps = new Bundle();
                initProps.putString("token", AccountManager.get().getToken());
                return initProps;
            }
        };
    }

    private static void callJsCallback(@NonNull ReactContext reactContext) {
        WeeeReactRouteModule module = reactContext
                .getNativeModule(WeeeReactRouteModule.class);
        if (module == null) return;
        WritableMap eventData = Arguments.createMap();
        eventData.putString("message", "android native call");
        module.sendEventToJS("onNativeEvent", eventData);
    }

}
