package com.sayweee.weee.module.debug;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.view.View;

import com.sayweee.logger.Logger;
import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.PreSaleManager;
import com.sayweee.weee.module.cart.service.BNPLHelper;
import com.sayweee.weee.module.debug.info.LocationTestActivity;
import com.sayweee.weee.module.debug.ui.CartOpActivity;
import com.sayweee.weee.module.debug.ui.ImageSearchTestActivity;
import com.sayweee.weee.module.debug.ui.SpreadTestActivity;
import com.sayweee.weee.module.debug.ui.VeilMineLoginTestActivity;
import com.sayweee.weee.module.debug.ui.VeilMineNotLoginTestActivity;
import com.sayweee.weee.module.debug.ui.VeilTestActivity;
import com.sayweee.weee.module.dialog.CompatDialog;
import com.sayweee.weee.module.dialog.LoadingCompatDialog;
import com.sayweee.weee.module.home.theme.ThemeManager;
import com.sayweee.weee.module.home.theme.listener.OnThemeEventListener;
import com.sayweee.weee.module.home.zipcode.AddressAddManuallyActivity;
import com.sayweee.weee.module.home.zipcode.AddressHelper;
import com.sayweee.weee.module.popup.PopupDialog;
import com.sayweee.weee.module.popup.PopupManager;
import com.sayweee.weee.module.popup.PopupSlideDialog;
import com.sayweee.weee.module.popup.bean.PopupConfigBean;
import com.sayweee.weee.module.thematic.ThematicActivity;
import com.sayweee.weee.module.web.WebRouter;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.module.web.fast.WebPreloadManager;
import com.sayweee.weee.react.ReactMainActivity;
import com.sayweee.weee.service.helper.PushHelper;
import com.sayweee.weee.utils.DateUtils;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.listener.OnDialogClickListener;
import com.sayweee.wrapper.widget.LoadingDialog;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/10/23.
 * Desc:    ui相关
 */
public class UiPanelActivity extends BaseDebugActivity<DebugViewModel> implements OnThemeEventListener {

    static final String TAG_SHOW_LOADING = "Loading";
    static final String TAG_SHOW_LOADING_COMPAT = "Loading Compat";
    static final String TAG_SHOW_LOADING_THEME = "Loading Theme";
    static final String TAG_SHOW_LOADING_OLD = "Loading Old";
    static final String TAG_SHOW_SPREAD = "Spread";
    static final String TAG_SHOW_NOTIFICATION = "Notification Apply";
    static final String TAG_SHOW_DIALOG = "TipsDialog";
    static final String TAG_CART_OP = "Cart Operate";
    static final String TAG_CLEAR_THEME = "清除主题";
    static final String TAG_DOWNLOAD_THEME = "加载主题";
    static final String TAG_APP_ACTIVITY = "获取活动";
    static final String TAG_ACTIVITY_TEST = "活动示例";
    static final String TAG_THEMATIC = "Thematic";
    static final String TAG_LIVE_PLAYER = "Live Player";
    static final String TAG_POPUP_CENTER = "popup_center";
    static final String TAG_POPUP_RATING = "popup_rating";
    static final String TAG_TITLE_JS = "title js";
    static final String LOCATION = "location";
    static final String TAG_VEIL = "Veil test";
    static final String TAG_ME_LOGIN = "Me login test";
    static final String TAG_ME_Not_LOGIN = "Me not login test";
    static final String TAG_ADDRESS_MAP = "address_map";
    static final String IMAGE_SEARCH = "image_search";
    static final String TAG_PREHEAT = "preheat";
    static final String TAG_REACT = "react native";

    public static Intent getIntent(Context context) {
        return new Intent(context, UiPanelActivity.class);
    }

    @Override
    protected void fillItem() {
        ThemeManager.get().registerThemeEventListener(this);
        addItems(
                TAG_REACT,
                TAG_SHOW_LOADING
                , TAG_SHOW_LOADING_COMPAT
                , TAG_SHOW_LOADING_THEME
                , TAG_SHOW_LOADING_OLD
                , TAG_SHOW_SPREAD
                , TAG_SHOW_NOTIFICATION
                , TAG_SHOW_DIALOG
                , TAG_CART_OP
                , TAG_CLEAR_THEME
                , TAG_DOWNLOAD_THEME
                , TAG_APP_ACTIVITY
                , TAG_ACTIVITY_TEST
                , TAG_THEMATIC
                , TAG_LIVE_PLAYER
                , TAG_POPUP_CENTER
                , TAG_POPUP_RATING
                , TAG_TITLE_JS
                , LOCATION
                , TAG_VEIL
                , TAG_ME_LOGIN
                , TAG_ME_Not_LOGIN
                , TAG_ADDRESS_MAP
                , IMAGE_SEARCH
                ,TAG_PREHEAT
        );
    }

    @Override
    public void onClick(String tag) {
        switch (tag) {
            case TAG_REACT:
//                Intent intent = new Intent(activity, ReactMainActivity.class);
                activity.startActivity(ReactMainActivity.getIntent(activity, "MyListPage"));
                break;
            case TAG_SHOW_LOADING:
                new LoadingCompatDialog(activity).show();
                BNPLHelper.fetchData();
                break;
            case TAG_SHOW_LOADING_COMPAT:
                new LoadingCompatDialog(activity, R.style.CommonDialogTheme).show();
                break;
            case TAG_SHOW_LOADING_THEME:
                showNormalLoading();
                break;
            case TAG_SHOW_LOADING_OLD:
                new LoadingDialog(activity).show();
                break;
            case TAG_SHOW_SPREAD:
                startActivity(new Intent(activity, SpreadTestActivity.class));
                break;
            case TAG_SHOW_NOTIFICATION:
                PreSaleManager.get().showPreSaleTips();
                PopupManager.get().showSoldOutTips();
                long zero = DateUtils.getWeekDayZero();
                Logger.json(zero);
                Logger.json(DateUtils.getFormatTime(DateUtils.YYYY_MM_DD_HH_MM_SS, zero, null));
                break;
            case TAG_SHOW_DIALOG:
                showTipsDialog();
                break;
            case TAG_CART_OP:
                startActivity(new Intent(activity, CartOpActivity.class));
                break;
            case TAG_CLEAR_THEME:
                ThemeManager.get().clear();
                break;
            case TAG_DOWNLOAD_THEME:
                ThemeManager.get().checkTheme();
                break;
            case TAG_APP_ACTIVITY:
                startActivity(WebViewActivity.getIntent(activity, "https://www.sayweee.com/home/<USER>"));
//                PopupManager.get().showSlide("https://www.sayweee.com/home/<USER>");
                PopupManager.get().showSlide(activity, "https://www.sayweee.com/activity/center?key=app_purchase_1st&type=coupon&ws=log_in&__showmode__=popup&__popup_height__=800");
                break;
            case TAG_ACTIVITY_TEST:
                new PopupDialog().showOnQueueLoaded("https://tb1.sayweee.net/activity/center?key=app_purchase_1st&type=coupon&ws=log_in");
                PopupManager.get().showOnQueue(new PopupDialog(200, 300).loadUrl("https://tb1.sayweee.net/activity/center?key=app_purchase_1st&type=coupon&ws=log_in"));
                PopupManager.get().showOnQueue(new PopupDialog().loadUrl("https://tb1.sayweee.net/home/<USER>"));
                PopupManager.get().showOnQueue(new PopupDialog().loadUrl("https://tb1.sayweee.net/home/<USER>"));
                break;
            case TAG_THEMATIC:
                startActivity(ThematicActivity.getIntent(activity));
                break;
            case TAG_LIVE_PLAYER:
                toLivePlayer();
                break;
            case TAG_POPUP_CENTER:
//                String url = "https://tb1.sayweee.net/activity/center?key=app_purchase_1st&type=coupon";
//                String url = "https://tb1.sayweee.net/activity/center?key=vip_special_offer&type=member";
//                String url = "https://www.sayweee.com/member/free_trial_landing?ws=me_page";
                // 测试账号 <EMAIL>/1234qwer
                String url = "https://tb1.sayweee.net/activity/center?key=vip_special_offer&type=member&ws=activity";
                PopupManager.get().showOnQueue(new PopupSlideDialog().loadUrl(new PopupConfigBean(url, true)), true);
//                new PopupDialogFragment().showOnQueueLoaded(url).show(getSupportFragmentManager(), "PopupDialogFragment");
                break;
            case TAG_POPUP_RATING:
                PopupManager.get().showOnQueue(new PopupSlideDialog().loadUrl(new PopupConfigBean("https://tb1.sayweee.net/zh/support/rating")), true);
                break;
            case TAG_TITLE_JS:
                startActivity(WebViewActivity.getIntent(activity, "https://tb1.sayweee.net/zh/review/callApp"));
                break;
            case TAG_ADDRESS_MAP:
                startActivity(AddressAddManuallyActivity.getIntent(activity, AddressHelper.Type.TYPE_ADD, true,null));
                break;
            case LOCATION:
                startActivity(LocationTestActivity.getIntent(activity));
                break;
            case TAG_VEIL:
                startActivity(VeilTestActivity.getIntent(activity));
                break;
            case TAG_ME_LOGIN:
                startActivity(VeilMineLoginTestActivity.getIntent(activity));
                break;
            case TAG_ME_Not_LOGIN:
                startActivity(VeilMineNotLoginTestActivity.getIntent(activity));
                break;
            case IMAGE_SEARCH:
                startActivity(ImageSearchTestActivity.getIntent(activity));
                break;
            case TAG_PREHEAT:
                WebRouter.toPage(this, WebPreloadManager.URL.PREHEAT);
                break;

        }
    }

    @Override
    protected void onDestroy() {
        ThemeManager.get().unregisterThemeEventListener(this);
        super.onDestroy();
    }

    private void showTipsDialog() {
        new CompatDialog(activity).setUp(new OnDialogClickListener() {
                    @Override
                    public void onClick(WrapperDialog dialog, View view) {
                        dialog.dismiss();
                    }
                }, "当前项目名称" + getResources().getString(R.string.app_name) + " ChannelId " + PushHelper.getChannelId() + " PushToken " + PushHelper.getPushToken(), "知道了")
                .show();
    }

    private void showNormalLoading() {
        new WrapperDialog(activity, R.style.AlertDialogTheme) {
            @Override
            public void help(ViewHelper helper) {

            }

            @Override
            protected int getLayoutRes() {
                return R.layout.dialog_loading_normal;
            }

            @Override
            protected void setDialogParams(Dialog dialog) {

            }
        }
                .show();
    }

    @Override
    public void onThemeStatusChanged(int status) {
        Logger.json("onThemeStatusChanged --> " + status);
        switch (status) {
            case ThemeManager.STATUS_THEME_CLEARED:
                Toaster.showToast("主题清除成功");
                break;
            case ThemeManager.STATUS_THEME_CHECK:
                Toaster.showToast("主题检查中...");
                break;
            case ThemeManager.STATUS_THEME_UPGRADE:
                Toaster.showToast("主题升级中...");
                break;
            case ThemeManager.STATUS_THEME_LOAD_SUCCESS:
                Toaster.showToast("主题下载成功");
                break;
            case ThemeManager.STATUS_THEME_READY:
                Toaster.showToast("主题已开启");
                break;
            case ThemeManager.STATUS_THEME_REMOVED:
                Toaster.showToast("主题已关闭");
                break;
            case ThemeManager.STATUS_THEME_ERROR:
                Toaster.showToast("主题加载出错");
                break;
            case ThemeManager.STATUS_THEME_FINISHED:
                break;
        }
    }

    private void toLivePlayer() {
        WebRouter.toPage(activity, "social/live");
    }
}
