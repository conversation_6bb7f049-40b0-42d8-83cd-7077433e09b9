pluginManagement { includeBuild("${settingReactNativePath}/node_modules/@react-native/gradle-plugin") }
plugins { id("com.facebook.react.settings") }

List<String> customCommand = [
        "npx",
        "@react-native-community/cli",
        "config"
]
File customWorkingDirectory = settings.layout.rootDirectory.dir("$settingReactNativePath").asFile
FileCollection customLockFiles = settings.layout.rootDirectory
        .dir(settingReactNativePath)
        .files("yarn.lock", "package-lock.json", "package.json", "react-native.config.js")

extensions.configure(com.facebook.react.ReactSettingsExtension){ ex -> ex.autolinkLibrariesFromCommand(
        customCommand, customWorkingDirectory, customLockFiles
) }
includeBuild("${settingReactNativePath}/node_modules/@react-native/gradle-plugin")


rootProject.name = "mobile-android"
include ':app'

if (ENABLE_MARCO_BENCHMARK == 'true') {
    include ':marcobenchmark'
}
if (ENABLE_BASELINE_PROFILE == 'true') {
    include ':baselineprofile'
}

include ':rtn-weee-module'
project(':rtn-weee-module').projectDir = new File("${settingReactNativePath}/node_modules/rtn-weee-module/android")
