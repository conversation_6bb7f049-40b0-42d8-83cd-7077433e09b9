package com.sayweee.react.network

import com.facebook.react.bridge.ReadableMap
import com.facebook.react.bridge.WritableMap
import com.sayweee.wrapper.bean.ResponseBean
import com.sayweee.wrapper.bean.SimpleResponseBean
import retrofit2.Call
import retrofit2.http.GET
import retrofit2.http.QueryMap
import retrofit2.http.Url


//
// Created by <PERSON><PERSON> on 10/07/2025.
//
interface RTNWeeeApi {

    @GET
    fun get(
        @Url url: String?, @QueryMap params: HashMap<String, Any?>?
    ): Call<SimpleResponseBean>?

}