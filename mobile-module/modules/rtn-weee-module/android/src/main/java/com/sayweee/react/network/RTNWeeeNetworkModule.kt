package com.sayweee.react.network

import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReadableMap
import com.facebook.react.bridge.WritableMap
import com.facebook.react.module.annotations.ReactModule
import com.sayweee.react.NativeRTNWeeeNetworkSpec
import com.sayweee.wrapper.http.RetrofitIml

@ReactModule(name = RTNWeeeNetworkModule.NAME)
class RTNWeeeNetworkModule(reactContext: ReactApplicationContext) :
    NativeRTNWeeeNetworkSpec(reactContext) {

  override fun getName(): String {
      return NAME
  }

  // Example method
  // See https://reactnative.dev/docs/native-modules-android
  override fun multiply(a: Double, b: Double): Double {
    return a * b
  }

    override fun request(config: ReadableMap?, promise: Promise?) {
        TODO("Not yet implemented")
    }

    override fun get(url: String?, config: ReadableMap?): WritableMap {
        val body = RetrofitIml.get().getHttpService(RTNWeeeApi::class.java)
            .get(url, config?.toHashMap())?.execute()?.body()
        val response = Arguments.createMap()
        body?.let {
            response.putBoolean("result", body.result)
            response.putString("message", body.message)
            response.putString("message_id", body.message_id)
            response.putString("data", body.data)
        }
        return response
    }



    override fun post(url: String?, data: ReadableMap?, config: ReadableMap?, promise: Promise?) {
        TODO("Not yet implemented")
    }

    override fun addListener(eventName: String?) {
        TODO("Not yet implemented")
    }

    override fun removeListeners(count: Double) {
        TODO("Not yet implemented")
    }

    companion object {
        const val NAME = "RTNWeeeNetwork"
    }
}
