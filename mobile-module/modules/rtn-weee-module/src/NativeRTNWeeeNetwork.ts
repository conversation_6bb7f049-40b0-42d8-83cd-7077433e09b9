import type { TurboModule } from 'react-native';
import { TurboModuleRegistry } from 'react-native';

// 定义请求配置的类型
export interface RequestConfig {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: {[key: string]: string};
  // 参数可以是任意可序列化的对象
  params?: {[key: string]: Object};
  // 请求体数据
  data?: {[key: string]: Object};
  timeout?: number; // 超时时间（毫秒）
}

// 响应类型
export interface Response {
  result: boolean;
  message: string;
  message_id: string;
  data: string;
}

export interface Spec extends TurboModule {
  multiply(a: number, b: number): number;

  request(config: RequestConfig): Promise<Response>;

  get(url: string, config?: RequestConfig): Response;
  post(url: string, data?: {[key: string]: Object}, config?: RequestConfig): Promise<Response>;

  // 添加事件监听器以处理全局事件（如网络状态变更）
  addListener(eventName: string): void;
  removeListeners(count: number): void;

}

export default TurboModuleRegistry.getEnforcing<Spec>('RTNWeeeNetwork');
